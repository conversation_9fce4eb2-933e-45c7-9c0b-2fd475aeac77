'use client';

import React, { useState, useEffect } from 'react';
import { useWaypointStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Download, MapPin, Clock, Zap, Mountain, Heart, Settings, Coins, Eye, User, LogIn, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { GPXGenerationOptions, downloadGpx, generateGpx } from '@/lib/gpx-clean';
import DataVisualization, { DataPoint, VisualizationStats } from './DataVisualization';
import TokenPurchase from './TokenPurchase';
import AuthForm from './AuthForm';

interface GPXFormData {
  name: string;
  description: string;
  activityType: 'Run' | 'Bike' | 'Walk';
  inputType: 'speed' | 'pace';
  averageSpeedKmh: number;
  averagePaceMinPerKm: number;
  elevationGain: number;
  elevationProfile: 'flat' | 'hilly' | 'mountainous';
  addNoise: boolean;
  pauseDuration: number;
  useRealisticTiming: boolean;
  samplingRateSeconds: number;
  speedVariation: number;
  useRealElevation: boolean;
  // Heart rate options
  includeHeartRate: boolean;
  userAge: number;
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced';
  averageHeartRate?: number;
  includeHRV: boolean;
  // Enhanced pace inconsistency
  paceInconsistency: 'low' | 'medium' | 'high';
  includeRestStops: boolean;
  fatigueEffect: boolean;
  // Start time
  startDate: string;
  startTime: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  tokenBalance: number;
}

export default function EnhancedGPXGenerator() {
  const { waypoints, routeGeometry, totalDistance } = useWaypointStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewData, setPreviewData] = useState<DataPoint[] | null>(null);
  const [previewStats, setPreviewStats] = useState<VisualizationStats | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showTokenPurchase, setShowTokenPurchase] = useState(false);
  const [showAuth, setShowAuth] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [user, setUser] = useState<User | null>(null);

  const [formData, setFormData] = useState<GPXFormData>({
    name: 'My Activity',
    description: 'Generated with realistic timing patterns and heart rate data',
    activityType: 'Run',
    inputType: 'speed',
    averageSpeedKmh: 12,
    averagePaceMinPerKm: 5,
    elevationGain: 50,
    elevationProfile: 'flat',
    addNoise: true,
    pauseDuration: 0,
    useRealisticTiming: true,
    samplingRateSeconds: 4,
    speedVariation: 0.15,
    useRealElevation: false,
    // Heart rate options
    includeHeartRate: true,
    userAge: 35,
    fitnessLevel: 'intermediate',
    averageHeartRate: undefined,
    includeHRV: true,
    // Enhanced pace inconsistency
    paceInconsistency: 'medium',
    includeRestStops: false,
    fatigueEffect: false,
    // Start time
    startDate: new Date().toISOString().split('T')[0],
    startTime: '08:00',
  });

  // Check for existing authentication on component mount
  useEffect(() => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      // Verify token and get user info
      fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })
      .then(res => res.json())
      .then(data => {
        if (data.user) {
          setUser(data.user);
        }
      })
      .catch(() => {
        // Token invalid, remove it
        localStorage.removeItem('auth-token');
        document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      });
    }
  }, []);

  const handleInputChange = (field: keyof GPXFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Auto-convert between speed and pace
      if (field === 'inputType') {
        if (value === 'pace' && prev.averageSpeedKmh) {
          newData.averagePaceMinPerKm = Math.round((60 / prev.averageSpeedKmh) * 10) / 10;
        } else if (value === 'speed' && prev.averagePaceMinPerKm) {
          newData.averageSpeedKmh = Math.round((60 / prev.averagePaceMinPerKm) * 10) / 10;
        }
      } else if (field === 'averageSpeedKmh' && prev.inputType === 'speed') {
        newData.averagePaceMinPerKm = Math.round((60 / (value as number)) * 10) / 10;
      } else if (field === 'averagePaceMinPerKm' && prev.inputType === 'pace') {
        newData.averageSpeedKmh = Math.round((60 / (value as number)) * 10) / 10;
      }

      return newData;
    });
  };

  const generatePreview = async () => {
    if (!routeGeometry?.coordinates || routeGeometry.coordinates.length < 2) {
      toast.error('Please create a route with at least 2 waypoints first.');
      return;
    }

    setIsGenerating(true);
    try {
      const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);

      const options: GPXGenerationOptions = {
        name: formData.name,
        description: formData.description,
        activityType: formData.activityType,
        coordinates: routeGeometry.coordinates,
        startTime: startDateTime,
        elevationGain: formData.elevationGain > 0 ? formData.elevationGain : undefined,
        elevationProfile: formData.elevationProfile,
        addNoise: formData.addNoise,
        pauseDuration: formData.pauseDuration,
        useRealisticTiming: formData.useRealisticTiming,
        samplingRateSeconds: formData.samplingRateSeconds,
        speedVariation: formData.speedVariation,
        useRealElevation: formData.useRealElevation,
        addElevation: formData.useRealElevation || formData.elevationGain > 0,
        // Heart rate options
        includeHeartRate: formData.includeHeartRate,
        userAge: formData.userAge,
        fitnessLevel: formData.fitnessLevel,
        averageHeartRate: formData.averageHeartRate,
        includeHRV: formData.includeHRV,
        // Enhanced pace inconsistency
        paceInconsistency: formData.paceInconsistency,
        includeRestStops: formData.includeRestStops,
        fatigueEffect: formData.fatigueEffect,
      };

      // Add speed or pace
      if (formData.inputType === 'speed') {
        options.averageSpeedKmh = formData.averageSpeedKmh;
      } else {
        options.averagePaceMinPerKm = formData.averagePaceMinPerKm;
      }

      // Generate GPX content for preview (without downloading)
      const gpxContent = await generateGpx(options);

      // Parse GPX content to extract data points for visualization
      // This is a simplified version - in a real app you'd parse the XML properly
      const mockDataPoints = generateMockDataPoints(options);
      const mockStats = generateMockStats(options);

      setPreviewData(mockDataPoints);
      setPreviewStats(mockStats);
      setShowPreview(true);

      toast.success('Preview generated!', {
        description: `${routeGeometry.coordinates.length} points, ${totalDistance.toFixed(1)}km route`
      });
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate preview', {
        description: 'Please check your route data and try again.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExport = async () => {
    if (!user) {
      setShowAuth(true);
      return;
    }

    if (user.tokenBalance < 1) {
      setShowTokenPurchase(true);
      return;
    }

    if (!routeGeometry?.coordinates || routeGeometry.coordinates.length < 2) {
      toast.error('Please create a route with at least 2 waypoints first.');
      return;
    }

    setIsGenerating(true);

    try {
      // Check token balance and deduct token
      const response = await fetch('/api/tokens/use', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
        },
        body: JSON.stringify({
          activityName: formData.name,
          activityType: formData.activityType,
          tokensRequired: 1,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to use token');
      }

      // Update user balance
      setUser(prev => prev ? { ...prev, tokenBalance: result.newBalance } : null);

      const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);

      const options: GPXGenerationOptions = {
        name: formData.name,
        description: formData.description,
        activityType: formData.activityType,
        coordinates: routeGeometry.coordinates,
        startTime: startDateTime,
        elevationGain: formData.elevationGain > 0 ? formData.elevationGain : undefined,
        elevationProfile: formData.elevationProfile,
        addNoise: formData.addNoise,
        pauseDuration: formData.pauseDuration,
        useRealisticTiming: formData.useRealisticTiming,
        samplingRateSeconds: formData.samplingRateSeconds,
        speedVariation: formData.speedVariation,
        useRealElevation: formData.useRealElevation,
        addElevation: formData.useRealElevation || formData.elevationGain > 0,
        // Heart rate options
        includeHeartRate: formData.includeHeartRate,
        userAge: formData.userAge,
        fitnessLevel: formData.fitnessLevel,
        averageHeartRate: formData.averageHeartRate,
        includeHRV: formData.includeHRV,
        // Enhanced pace inconsistency
        paceInconsistency: formData.paceInconsistency,
        includeRestStops: formData.includeRestStops,
        fatigueEffect: formData.fatigueEffect,
      };

      // Add speed or pace
      if (formData.inputType === 'speed') {
        options.averageSpeedKmh = formData.averageSpeedKmh;
      } else {
        options.averagePaceMinPerKm = formData.averagePaceMinPerKm;
      }

      await downloadGpx(options);

      toast.success('GPX file exported successfully!', {
        description: `${formData.name} downloaded. Remaining tokens: ${result.newBalance}`,
      });
    } catch (error) {
      console.error('Error exporting GPX:', error);
      toast.error('Failed to export GPX file', {
        description: error instanceof Error ? error.message : 'Please try again.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAuthSuccess = (userData: User) => {
    setUser(userData);
    setShowAuth(false);
    toast.success('Welcome!', {
      description: `You have ${userData.tokenBalance} tokens available.`,
    });
  };

  const handleTokenPurchaseSuccess = (newBalance: number) => {
    setUser(prev => prev ? { ...prev, tokenBalance: newBalance } : null);
    setShowTokenPurchase(false);
  };

  // Mock data generation functions for preview
  const generateMockDataPoints = (options: GPXGenerationOptions): DataPoint[] => {
    const points: DataPoint[] = [];
    const numPoints = Math.min(routeGeometry?.coordinates.length || 0, 100);
    const speed = options.averageSpeedKmh || 60 / (options.averagePaceMinPerKm || 5);

    for (let i = 0; i < numPoints; i++) {
      const timeSeconds = i * 30; // 30 seconds between points
      const distance = (speed * timeSeconds) / 3600; // km

      // Add some variation
      const speedVariation = 0.8 + Math.random() * 0.4;
      const currentSpeed = speed * speedVariation;
      const currentPace = 60 / currentSpeed;

      // Mock elevation
      const elevation = 100 + Math.sin(i / 10) * (options.elevationGain || 50) / 2;

      // Mock heart rate
      const heartRate = options.includeHeartRate
        ? 120 + Math.sin(i / 20) * 30 + (Math.random() - 0.5) * 10
        : undefined;

      points.push({
        time: formatTime(timeSeconds),
        timeSeconds,
        distance,
        pace: currentPace,
        speed: currentSpeed,
        elevation,
        heartRate: heartRate ? Math.round(heartRate) : undefined,
      });
    }

    return points;
  };

  const generateMockStats = (options: GPXGenerationOptions): VisualizationStats => {
    const speed = options.averageSpeedKmh || 60 / (options.averagePaceMinPerKm || 5);
    const duration = (totalDistance / speed) * 3600;

    return {
      totalDistance,
      totalTime: formatTime(duration),
      averagePace: 60 / speed,
      averageSpeed: speed,
      elevationGain: options.elevationGain || 0,
      averageHeartRate: options.includeHeartRate ? 150 : undefined,
      maxHeartRate: options.includeHeartRate ? 180 : undefined,
      averageHRV: options.includeHRV ? 35 : undefined,
    };
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const canGenerate = routeGeometry?.coordinates && routeGeometry.coordinates.length >= 2;

  // Show authentication modal
  if (showAuth) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <AuthForm
            mode={authMode}
            onSuccess={handleAuthSuccess}
            onModeChange={setAuthMode}
          />
          <Button
            variant="outline"
            onClick={() => setShowAuth(false)}
            className="w-full mt-4"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  // Show token purchase modal
  if (showTokenPurchase && user) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <TokenPurchase
            currentBalance={user.tokenBalance}
            onPurchaseSuccess={handleTokenPurchaseSuccess}
            onClose={() => setShowTokenPurchase(false)}
          />
          <Button
            variant="outline"
            onClick={() => setShowTokenPurchase(false)}
            className="w-full mt-4"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  // Show data visualization modal
  if (showPreview && previewData && previewStats) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">Activity Preview</h2>
            <Button
              variant="outline"
              onClick={() => setShowPreview(false)}
            >
              Close
            </Button>
          </div>
          <DataVisualization
            data={previewData}
            stats={previewStats}
            activityType={formData.activityType}
            showHeartRate={formData.includeHeartRate}
          />
          <div className="flex gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowPreview(false)}
              className="flex-1"
            >
              Close Preview
            </Button>
            <Button
              onClick={() => {
                setShowPreview(false);
                handleExport();
              }}
              disabled={!user || user.tokenBalance < 1}
              className="flex-1"
            >
              <Download className="mr-2 h-4 w-4" />
              Export GPX (1 token)
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-6 w-6 text-red-500" />
              Enhanced GPX Generator
            </CardTitle>
            <CardDescription>
              Generate realistic GPX files with heart rate data, pace inconsistency, and data visualization
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            {user ? (
              <div className="text-right">
                <div className="text-sm font-medium">{user.name}</div>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Coins className="h-4 w-4 text-yellow-500" />
                  {user.tokenBalance} tokens
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAuth(true)}
              >
                <LogIn className="mr-2 h-4 w-4" />
                Login
              </Button>
            )}
            {user && user.tokenBalance < 5 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTokenPurchase(true)}
              >
                <Coins className="mr-2 h-4 w-4" />
                Buy Tokens
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Route Info */}
        {canGenerate && (
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Current Route
            </h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>Waypoints: {waypoints.length}</div>
              <div>Distance: {totalDistance.toFixed(2)} km</div>
              <div>Type: {routeGeometry?.isRouted ? 'Routed' : 'Straight line'}</div>
            </div>
          </div>
        )}

        {!canGenerate && (
          <Alert>
            <MapPin className="h-4 w-4" />
            <AlertDescription>
              Please create a route with at least 2 waypoints using the map above to generate a GPX file.
            </AlertDescription>
          </Alert>
        )}

        {/* Basic Settings */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Activity Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="activity-type">Activity Type</Label>
            <Select
              value={formData.activityType}
              onValueChange={(value: 'Run' | 'Bike' | 'Walk') => handleInputChange('activityType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Run">🏃 Running</SelectItem>
                <SelectItem value="Bike">🚴 Cycling</SelectItem>
                <SelectItem value="Walk">🚶 Walking</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Start Date and Time */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="start-date">Start Date</Label>
            <Input
              id="start-date"
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="start-time">Start Time</Label>
            <Input
              id="start-time"
              type="time"
              value={formData.startTime}
              onChange={(e) => handleInputChange('startTime', e.target.value)}
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <Label htmlFor="description">Description</Label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Activity description..."
            rows={2}
            className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>

        <Separator />

        {/* Speed/Pace Settings */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Speed & Pace
          </h4>
          <div className="flex items-center space-x-4">
            <Label>Input Type:</Label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="inputType"
                  value="speed"
                  checked={formData.inputType === 'speed'}
                  onChange={(e) => handleInputChange('inputType', e.target.value)}
                />
                <span>Speed (km/h)</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="inputType"
                  value="pace"
                  checked={formData.inputType === 'pace'}
                  onChange={(e) => handleInputChange('inputType', e.target.value)}
                />
                <span>Pace (min/km)</span>
              </label>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {formData.inputType === 'speed' ? (
              <div>
                <Label htmlFor="speed">Average Speed (km/h)</Label>
                <Input
                  id="speed"
                  type="number"
                  min="1"
                  max="50"
                  step="0.5"
                  value={formData.averageSpeedKmh}
                  onChange={(e) => handleInputChange('averageSpeedKmh', parseFloat(e.target.value) || 12)}
                />
              </div>
            ) : (
              <div>
                <Label htmlFor="pace">Average Pace (min/km)</Label>
                <Input
                  id="pace"
                  type="number"
                  min="2"
                  max="20"
                  step="0.1"
                  value={formData.averagePaceMinPerKm}
                  onChange={(e) => handleInputChange('averagePaceMinPerKm', parseFloat(e.target.value) || 5)}
                />
              </div>
            )}
            <div className="flex items-end">
              <div className="text-sm text-muted-foreground">
                {formData.inputType === 'speed'
                  ? `≈ ${(60 / formData.averageSpeedKmh).toFixed(1)} min/km`
                  : `≈ ${formData.averageSpeedKmh.toFixed(1)} km/h`
                }
              </div>
            </div>
          </div>

          {/* Pace Inconsistency */}
          <div>
            <Label htmlFor="pace-inconsistency">Pace Inconsistency Level</Label>
            <Select
              value={formData.paceInconsistency}
              onValueChange={(value: 'low' | 'medium' | 'high') => handleInputChange('paceInconsistency', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low (±5% variation)</SelectItem>
                <SelectItem value="medium">Medium (±15% variation)</SelectItem>
                <SelectItem value="high">High (±25% variation)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="fatigue-effect"
                checked={formData.fatigueEffect}
                onCheckedChange={(checked) => handleInputChange('fatigueEffect', checked)}
              />
              <Label htmlFor="fatigue-effect">Gradual fatigue effect</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="rest-stops"
                checked={formData.includeRestStops}
                onCheckedChange={(checked) => handleInputChange('includeRestStops', checked)}
              />
              <Label htmlFor="rest-stops">Include rest stops</Label>
            </div>
          </div>
        </div>

        <Separator />

        {/* Heart Rate Settings */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Heart className="h-4 w-4 text-red-500" />
            Heart Rate Data
          </h4>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-heart-rate"
              checked={formData.includeHeartRate}
              onCheckedChange={(checked) => handleInputChange('includeHeartRate', checked)}
            />
            <Label htmlFor="include-heart-rate">Include heart rate data</Label>
          </div>

          {formData.includeHeartRate && (
            <div className="grid grid-cols-2 gap-4 ml-6">
              <div>
                <Label htmlFor="user-age">Age (for max HR calculation)</Label>
                <Input
                  id="user-age"
                  type="number"
                  min="18"
                  max="80"
                  value={formData.userAge}
                  onChange={(e) => handleInputChange('userAge', parseInt(e.target.value) || 35)}
                />
              </div>
              <div>
                <Label htmlFor="fitness-level">Fitness Level</Label>
                <Select
                  value={formData.fitnessLevel}
                  onValueChange={(value: 'beginner' | 'intermediate' | 'advanced') => handleInputChange('fitnessLevel', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="average-heart-rate">Average HR (optional)</Label>
                <Input
                  id="average-heart-rate"
                  type="number"
                  min="100"
                  max="200"
                  placeholder="Auto-calculated"
                  value={formData.averageHeartRate || ''}
                  onChange={(e) => handleInputChange('averageHeartRate', e.target.value ? parseInt(e.target.value) : undefined)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-hrv"
                  checked={formData.includeHRV}
                  onCheckedChange={(checked) => handleInputChange('includeHRV', checked)}
                />
                <Label htmlFor="include-hrv">Include HRV data</Label>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Elevation Settings */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Mountain className="h-4 w-4" />
            Elevation Data
          </h4>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="elevation-gain">Elevation Gain (meters)</Label>
              <Input
                id="elevation-gain"
                type="number"
                min="0"
                max="2000"
                value={formData.elevationGain}
                onChange={(e) => handleInputChange('elevationGain', parseInt(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="elevation-profile">Elevation Profile</Label>
              <Select
                value={formData.elevationProfile}
                onValueChange={(value: 'flat' | 'hilly' | 'mountainous') => handleInputChange('elevationProfile', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flat">🏞️ Flat</SelectItem>
                  <SelectItem value="hilly">🏔️ Hilly</SelectItem>
                  <SelectItem value="mountainous">⛰️ Mountainous</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="real-elevation"
              checked={formData.useRealElevation}
              onCheckedChange={(checked) => handleInputChange('useRealElevation', checked)}
            />
            <Label htmlFor="real-elevation">Use real elevation data (Open-Elevation API)</Label>
          </div>

          {formData.useRealElevation && (
            <div className="ml-6 text-sm text-muted-foreground">
              <p>⚠️ This will fetch real elevation data from Open-Elevation API</p>
              <p>• May take a few seconds for longer routes</p>
              <p>• Falls back to simulated elevation if API fails</p>
            </div>
          )}
        </div>

        <Separator />

        {/* Advanced Settings */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Advanced Settings
          </h4>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="realistic-timing"
              checked={formData.useRealisticTiming}
              onCheckedChange={(checked) => handleInputChange('useRealisticTiming', checked)}
            />
            <Label htmlFor="realistic-timing">Use realistic human-like pacing patterns</Label>
          </div>

          {formData.useRealisticTiming && (
            <div className="grid grid-cols-2 gap-4 ml-6">
              <div>
                <Label htmlFor="speed-variation">Speed Variation (0.05-0.5)</Label>
                <Input
                  id="speed-variation"
                  type="number"
                  min="0.05"
                  max="0.5"
                  step="0.05"
                  value={formData.speedVariation}
                  onChange={(e) => handleInputChange('speedVariation', parseFloat(e.target.value) || 0.15)}
                />
              </div>
              <div>
                <Label htmlFor="sampling-rate">GPS Sampling Rate (seconds)</Label>
                <Input
                  id="sampling-rate"
                  type="number"
                  min="1"
                  max="10"
                  value={formData.samplingRateSeconds}
                  onChange={(e) => handleInputChange('samplingRateSeconds', parseInt(e.target.value) || 4)}
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="add-noise"
                checked={formData.addNoise}
                onCheckedChange={(checked) => handleInputChange('addNoise', checked)}
              />
              <Label htmlFor="add-noise">Add speed variations</Label>
            </div>
            <div>
              <Label htmlFor="pause-duration">Pause Duration (seconds)</Label>
              <Input
                id="pause-duration"
                type="number"
                min="0"
                max="300"
                value={formData.pauseDuration}
                onChange={(e) => handleInputChange('pauseDuration', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            onClick={generatePreview}
            disabled={!canGenerate || isGenerating}
            variant="outline"
            className="flex-1"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Preview Data
              </>
            )}
          </Button>
          <Button
            onClick={handleExport}
            disabled={!canGenerate || isGenerating}
            className="flex-1"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export GPX {user ? `(1 token)` : '(Login required)'}
              </>
            )}
          </Button>
        </div>

        {/* Token requirement notice */}
        {!user && (
          <Alert>
            <User className="h-4 w-4" />
            <AlertDescription>
              Please log in to export GPX files. New users get 5 free tokens!
            </AlertDescription>
          </Alert>
        )}

        {user && user.tokenBalance === 0 && (
          <Alert>
            <Coins className="h-4 w-4" />
            <AlertDescription>
              You need tokens to export GPX files.
              <Button
                variant="link"
                className="p-0 h-auto ml-1"
                onClick={() => setShowTokenPurchase(true)}
              >
                Purchase tokens here
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Info */}
        <div className="text-xs text-muted-foreground space-y-1 border-t pt-4">
          <p>• <strong>Heart Rate:</strong> Realistic HR patterns based on age and fitness level</p>
          <p>• <strong>Pace Inconsistency:</strong> Natural speed variations throughout the activity</p>
          <p>• <strong>Data Visualization:</strong> Preview pace, elevation, and heart rate before export</p>
          <p>• <strong>Real Elevation:</strong> Fetches actual elevation data from Open-Elevation API</p>
          <p>• <strong>Token System:</strong> Each GPX export costs 1 token to support development</p>
        </div>
      </CardContent>
    </Card>
  );
}