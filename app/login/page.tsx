'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AuthForm from '@/components/AuthForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function LoginPage() {
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const router = useRouter();

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      // Verify token
      fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })
      .then(res => res.json())
      .then(data => {
        if (data.user) {
          router.push('/');
        }
      })
      .catch(() => {
        // Token invalid, remove it
        localStorage.removeItem('auth-token');
        document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      });
    }
  }, [router]);

  const handleAuthSuccess = (user: any) => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to App
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                FakeStrava
              </h1>
              <p className="text-gray-600 text-sm">
                Generate realistic GPX files with heart rate data
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md">
          <AuthForm
            mode={authMode}
            onSuccess={handleAuthSuccess}
            onModeChange={setAuthMode}
          />
          
          <div className="mt-8 text-center">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Why create an account?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex items-start gap-3">
                  <span className="text-green-500 font-bold">🎁</span>
                  <div>
                    <strong>5 Free Tokens</strong>
                    <p className="text-gray-600">Get started with 5 free GPX exports</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-500 font-bold">💾</span>
                  <div>
                    <strong>Save Your Settings</strong>
                    <p className="text-gray-600">Keep your preferences and activity history</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-purple-500 font-bold">📊</span>
                  <div>
                    <strong>Advanced Features</strong>
                    <p className="text-gray-600">Access heart rate data and data visualization</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-red-500 font-bold">🔒</span>
                  <div>
                    <strong>Secure & Private</strong>
                    <p className="text-gray-600">Your data is encrypted and never shared</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <footer className="bg-white border-t py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-600">
            <p>© 2024 FakeStrava. Generate realistic GPX files for testing and development.</p>
            <p className="mt-2">
              Built with Next.js, TypeScript, and ❤️
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
