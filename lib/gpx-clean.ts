/**
 * Clean GPX Generation Library
 * Enhanced with heart rate simulation and pace inconsistency
 */

import { distance } from '@turf/distance';
import { point } from '@turf/helpers';
import { generateHeartRateData, HeartRateData } from './heart-rate';

// Enhanced GPX Point interface
export interface GPXPoint {
  lat: number;
  lng: number;
  elevation?: number;
  time?: Date;
  heartRate?: number;
  hrv?: number; // heart rate variability in ms
  speed?: number; // speed in km/h
  pace?: number; // pace in min/km
}

// Enhanced GPX generation options
export interface GPXGenerationOptions {
  name: string;
  description?: string;
  activityType: 'Run' | 'Bike' | 'Walk';
  coordinates: [number, number][]; // [lng, lat] format from routing
  startTime?: Date;
  // Speed/pace options (mutually exclusive)
  averageSpeedKmh?: number; // km/h
  averagePaceMinPerKm?: number; // minutes per km
  // Elevation options
  elevationGain?: number; // total elevation gain in meters
  elevationProfile?: 'flat' | 'hilly' | 'mountainous';
  // Advanced options
  addNoise?: boolean; // Add realistic speed variations
  pauseDuration?: number; // Add pauses in seconds
  // Realistic timing options
  useRealisticTiming?: boolean; // Use human-like pacing patterns
  samplingRateSeconds?: number; // GPS sampling rate (default: 4 seconds)
  speedVariation?: number; // Speed variation factor (default: 0.15 = 15%)
  // Elevation options
  addElevation?: boolean; // Add elevation data to GPX
  useRealElevation?: boolean; // Use real elevation API instead of simulated
  useElevationAdjustedPacing?: boolean; // Adjust speed based on elevation changes
  // Heart rate options
  includeHeartRate?: boolean; // Add heart rate data
  userAge?: number; // For calculating max HR (default: 35)
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced'; // Fitness level
  averageHeartRate?: number; // Override automatic HR calculation
  includeHRV?: boolean; // Include heart rate variability
  // Enhanced pace inconsistency
  paceInconsistency?: 'low' | 'medium' | 'high'; // Level of pace variation
  includeRestStops?: boolean; // Add realistic rest stops
  fatigueEffect?: boolean; // Gradual slowdown over time
}

/**
 * Main GPX generation function
 */
export async function generateGpx(options: GPXGenerationOptions): Promise<string> {
  const {
    name,
    description = '',
    activityType,
    coordinates,
    startTime = new Date(),
    averageSpeedKmh,
    averagePaceMinPerKm,
    elevationGain,
    elevationProfile = 'flat',
    addNoise = true,
    pauseDuration = 0,
    useRealisticTiming = false,
    samplingRateSeconds = 4,
    speedVariation = 0.15,
    addElevation = false,
    useRealElevation = false,
    useElevationAdjustedPacing = false,
    // Heart rate options
    includeHeartRate = false,
    userAge = 35,
    fitnessLevel = 'intermediate',
    averageHeartRate,
    includeHRV = false,
    // Enhanced pace inconsistency
    paceInconsistency = 'medium',
    includeRestStops = false,
    fatigueEffect = false,
  } = options;

  if (!coordinates || coordinates.length < 2) {
    throw new Error('At least 2 coordinates are required to generate a GPX file');
  }

  // Calculate speed from pace if pace is provided
  let speedKmh = averageSpeedKmh;
  if (!speedKmh && averagePaceMinPerKm) {
    speedKmh = 60 / averagePaceMinPerKm;
  }
  if (!speedKmh) {
    speedKmh = getDefaultSpeed(activityType);
  }

  // Generate track points with enhanced features
  const gpxPoints = await generateEnhancedTrackPoints(
    coordinates,
    startTime,
    speedKmh,
    {
      elevationGain,
      elevationProfile,
      addNoise,
      pauseDuration,
      includeHeartRate,
      userAge,
      fitnessLevel,
      averageHeartRate,
      includeHRV,
      paceInconsistency,
      includeRestStops,
      fatigueEffect,
      activityType,
      useRealElevation,
      addElevation
    }
  );

  // Generate GPX XML
  return generateGPXXML(name, description, activityType, gpxPoints, startTime);
}

/**
 * Enhanced track points generation with heart rate and improved pace inconsistency
 */
async function generateEnhancedTrackPoints(
  coordinates: [number, number][],
  startTime: Date,
  speedKmh: number,
  options: {
    elevationGain?: number;
    elevationProfile?: 'flat' | 'hilly' | 'mountainous';
    addNoise?: boolean;
    pauseDuration?: number;
    includeHeartRate?: boolean;
    userAge?: number;
    fitnessLevel?: 'beginner' | 'intermediate' | 'advanced';
    averageHeartRate?: number;
    includeHRV?: boolean;
    paceInconsistency?: 'low' | 'medium' | 'high';
    includeRestStops?: boolean;
    fatigueEffect?: boolean;
    activityType?: 'Run' | 'Bike' | 'Walk';
    useRealElevation?: boolean;
    addElevation?: boolean;
  }
): Promise<GPXPoint[]> {
  const {
    elevationGain,
    elevationProfile = 'flat',
    addNoise = true,
    pauseDuration = 0,
    includeHeartRate = false,
    userAge = 35,
    fitnessLevel = 'intermediate',
    averageHeartRate,
    includeHRV = false,
    paceInconsistency = 'medium',
    includeRestStops = false,
    fatigueEffect = false,
    activityType = 'Run',
    useRealElevation = false,
    addElevation = false
  } = options;

  if (coordinates.length < 2) {
    return coordinates.map(([lng, lat]) => ({ lat, lng, time: startTime }));
  }

  const points: GPXPoint[] = [];
  let currentTime = new Date(startTime);
  let totalDistance = 0;

  // Calculate total route distance and duration for heart rate generation
  const totalRouteDistance = calculateCoordinatesDistance(coordinates);
  const estimatedDuration = (totalRouteDistance / speedKmh) * 3600; // in seconds

  // Generate enhanced pace variations based on inconsistency level
  const paceVariations = generatePaceVariations(coordinates.length, paceInconsistency, fatigueEffect);

  // Get elevation data if needed
  let elevationData: number[] = [];
  if (addElevation) {
    elevationData = coordinates.map((_, i) => calculateElevation(
      i, coordinates.length, 0, totalRouteDistance, elevationGain || 0, elevationProfile
    ));
  }

  // Generate heart rate data if requested
  let heartRateData: HeartRateData[] = [];
  if (includeHeartRate) {
    const timestamps = coordinates.map((_, i) => {
      const timeOffset = (i / (coordinates.length - 1)) * estimatedDuration * 1000;
      return new Date(startTime.getTime() + timeOffset);
    });

    heartRateData = generateHeartRateData(timestamps, {
      activityType,
      duration: estimatedDuration,
      userAge,
      fitnessLevel,
      averageHeartRate,
      includeHRV
    });
  }

  for (let i = 0; i < coordinates.length; i++) {
    const [lng, lat] = coordinates[i];

    // Get elevation for this point
    const elevation = elevationData.length > 0 ? elevationData[i] : undefined;

    // Calculate enhanced speed with pace inconsistency
    let currentSpeed = speedKmh;
    if (addNoise) {
      currentSpeed *= paceVariations[i];

      // Adjust speed based on elevation change if elevation is available
      if (elevation !== undefined && i > 0 && elevationData[i - 1] !== undefined) {
        const elevationChange = elevation - elevationData[i - 1];
        const segmentDistance = i > 0 ? calculateSegmentDistance(coordinates[i - 1], coordinates[i]) : 0;
        const elevationFactor = calculateSpeedAdjustmentForElevation(elevationChange, segmentDistance);
        currentSpeed *= elevationFactor;
      }
    }

    // Add the enhanced point
    const point: GPXPoint = {
      lat,
      lng,
      elevation,
      time: new Date(currentTime),
      speed: currentSpeed,
      pace: 60 / currentSpeed, // convert speed to pace
    };

    // Add heart rate data if available
    if (heartRateData.length > 0 && heartRateData[i]) {
      point.heartRate = heartRateData[i].heartRate;
      if (heartRateData[i].hrv) {
        point.hrv = heartRateData[i].hrv;
      }
    }

    points.push(point);

    // Calculate time to next point (if not the last point)
    if (i < coordinates.length - 1) {
      const segmentDistance = calculateSegmentDistance(coordinates[i], coordinates[i + 1]);
      totalDistance += segmentDistance;

      // Use the current speed for time calculation
      const segmentTimeHours = segmentDistance / currentSpeed;
      const segmentTimeMs = segmentTimeHours * 3600 * 1000;

      // Add rest stops occasionally for realism
      let pauseMs = 0;
      if (includeRestStops && Math.random() < 0.05) { // 5% chance of rest stop
        pauseMs = (30 + Math.random() * 120) * 1000; // 30-150 second rest
      } else if (pauseDuration > 0 && Math.random() < 0.1) { // 10% chance of regular pause
        pauseMs = pauseDuration * 1000 * (0.5 + Math.random() * 0.5); // 50-100% of pause duration
      }

      currentTime = new Date(currentTime.getTime() + segmentTimeMs + pauseMs);
    }
  }

  return points;
}

// Generate pace variations based on inconsistency level
function generatePaceVariations(
  pointCount: number, 
  inconsistency: 'low' | 'medium' | 'high',
  fatigueEffect: boolean
): number[] {
  const variations: number[] = [];
  
  // Base variation ranges
  const variationRanges = {
    low: { min: 0.95, max: 1.05 },     // ±5%
    medium: { min: 0.85, max: 1.15 },  // ±15%
    high: { min: 0.75, max: 1.25 }     // ±25%
  };
  
  const range = variationRanges[inconsistency];
  
  for (let i = 0; i < pointCount; i++) {
    const progress = i / (pointCount - 1);
    
    // Base random variation
    let variation = range.min + Math.random() * (range.max - range.min);
    
    // Apply fatigue effect (gradual slowdown)
    if (fatigueEffect) {
      const fatigueMultiplier = 1 - (progress * 0.15); // Up to 15% slowdown
      variation *= fatigueMultiplier;
    }
    
    // Add some smoothing to avoid extreme jumps
    if (i > 0) {
      const previousVariation = variations[i - 1];
      const maxChange = 0.1; // Maximum 10% change between points
      const change = variation - previousVariation;
      if (Math.abs(change) > maxChange) {
        variation = previousVariation + Math.sign(change) * maxChange;
      }
    }
    
    variations.push(variation);
  }
  
  return variations;
}

// Helper functions
function calculateCoordinatesDistance(coordinates: [number, number][]): number {
  let totalDistance = 0;
  for (let i = 1; i < coordinates.length; i++) {
    const [lng1, lat1] = coordinates[i - 1];
    const [lng2, lat2] = coordinates[i];
    const segmentDistance = distance(point([lng1, lat1]), point([lng2, lat2]));
    totalDistance += segmentDistance;
  }
  return totalDistance;
}

function calculateSegmentDistance(coord1: [number, number], coord2: [number, number]): number {
  const [lng1, lat1] = coord1;
  const [lng2, lat2] = coord2;
  return distance(point([lng1, lat1]), point([lng2, lat2]));
}

function calculateElevation(
  index: number,
  totalPoints: number,
  currentDistance: number,
  totalDistance: number,
  elevationGain: number,
  profile: 'flat' | 'hilly' | 'mountainous'
): number {
  const progress = index / (totalPoints - 1);
  const baseElevation = 100;

  if (profile === 'flat') {
    return baseElevation + (Math.random() - 0.5) * 10;
  }

  const profileMultipliers = {
    hilly: 1,
    mountainous: 2
  };

  const multiplier = profileMultipliers[profile] || 1;
  const variation = Math.sin(progress * Math.PI * 2) * elevationGain * multiplier * 0.5;
  const randomNoise = (Math.random() - 0.5) * 20;

  return Math.max(0, baseElevation + variation + randomNoise);
}

function calculateSpeedAdjustmentForElevation(elevationChange: number, distance: number): number {
  if (distance === 0) return 1;

  const grade = elevationChange / (distance * 1000); // Convert to grade

  if (grade > 0.02) { // Uphill > 2%
    return Math.max(0.7, 1 - grade * 5); // Slow down
  } else if (grade < -0.02) { // Downhill > 2%
    return Math.min(1.2, 1 + Math.abs(grade) * 3); // Speed up
  }

  return 1; // No significant change
}

function getDefaultSpeed(activityType: 'Run' | 'Bike' | 'Walk'): number {
  const defaults = {
    'Run': 12,
    'Bike': 25,
    'Walk': 5
  };
  return defaults[activityType];
}

// Generate GPX XML from track points
function generateGPXXML(
  name: string,
  description: string,
  activityType: string,
  points: GPXPoint[],
  startTime: Date
): string {
  const formatDate = (date: Date) => date.toISOString();
  const escapeXml = (str: string) => str.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case "'": return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });

  const trackPoints = points.map((point) => {
    const elevationTag = point.elevation !== undefined ? `<ele>${point.elevation}</ele>` : '';
    const timeTag = point.time ? `<time>${formatDate(point.time)}</time>` : '';

    // Add heart rate extensions if available
    let extensionsTag = '';
    if (point.heartRate || point.hrv || point.speed) {
      const heartRateTag = point.heartRate ? `<gpxtpx:hr>${point.heartRate}</gpxtpx:hr>` : '';
      const speedTag = point.speed ? `<gpxtpx:speed>${point.speed.toFixed(2)}</gpxtpx:speed>` : '';
      const hrvTag = point.hrv ? `<gpxtpx:hrv>${point.hrv}</gpxtpx:hrv>` : '';

      if (heartRateTag || speedTag || hrvTag) {
        extensionsTag = `<extensions>
        <gpxtpx:TrackPointExtension>
          ${heartRateTag}
          ${speedTag}
          ${hrvTag}
        </gpxtpx:TrackPointExtension>
      </extensions>`;
      }
    }

    return `    <trkpt lat="${point.lat.toFixed(6)}" lon="${point.lng.toFixed(6)}">
      ${elevationTag}
      ${timeTag}
      ${extensionsTag}
    </trkpt>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<gpx version="1.1" creator="FakeStrava GPX Generator"
     xmlns="http://www.topografix.com/GPX/1/1"
     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xmlns:gpxtpx="http://www.garmin.com/xmlschemas/TrackPointExtension/v1"
     xsi:schemaLocation="http://www.topografix.com/GPX/1/1 http://www.topografix.com/GPX/1/1/gpx.xsd http://www.garmin.com/xmlschemas/TrackPointExtension/v1 http://www.garmin.com/xmlschemas/TrackPointExtensionv1.xsd">
  <metadata>
    <name>${escapeXml(name)}</name>
    <desc>${escapeXml(description)}</desc>
    <time>${formatDate(startTime)}</time>
    <author>
      <name>FakeStrava</name>
    </author>
  </metadata>
  <trk>
    <name>${escapeXml(name)}</name>
    <type>${escapeXml(activityType)}</type>
    <trkseg>
${trackPoints}
    </trkseg>
  </trk>
</gpx>`;
}

/**
 * Enhanced download function with options
 */
export async function downloadGpx(options: GPXGenerationOptions): Promise<void> {
  const gpxContent = await generateGpx(options);
  const blob = new Blob([gpxContent], { type: 'application/gpx+xml' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `${options.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.gpx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
}
