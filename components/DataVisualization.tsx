'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
  ComposedChart,
  Bar
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export interface DataPoint {
  time: string; // formatted time (e.g., "00:05:30")
  timeSeconds: number; // time in seconds from start
  distance: number; // cumulative distance in km
  pace: number; // current pace in min/km
  speed: number; // current speed in km/h
  elevation: number; // elevation in meters
  heartRate?: number; // heart rate in bpm
  hrv?: number; // heart rate variability in ms
}

export interface VisualizationStats {
  totalDistance: number;
  totalTime: string;
  averagePace: number;
  averageSpeed: number;
  elevationGain: number;
  averageHeartRate?: number;
  maxHeartRate?: number;
  averageHRV?: number;
}

interface DataVisualizationProps {
  data: DataPoint[];
  stats: VisualizationStats;
  activityType: 'Run' | 'Bike' | 'Walk';
  showHeartRate?: boolean;
}

export default function DataVisualization({
  data,
  stats,
  activityType,
  showHeartRate = false
}: DataVisualizationProps) {
  // Format time for display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Format pace for display
  const formatPace = (pace: number) => {
    const minutes = Math.floor(pace);
    const seconds = Math.round((pace - minutes) * 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{`Time: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.name}: ${
                entry.dataKey === 'pace' 
                  ? formatPace(entry.value)
                  : entry.dataKey === 'heartRate'
                  ? `${entry.value} bpm`
                  : entry.dataKey === 'elevation'
                  ? `${entry.value} m`
                  : entry.dataKey === 'speed'
                  ? `${entry.value.toFixed(1)} km/h`
                  : entry.value
              }`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Distance</CardDescription>
            <CardTitle className="text-2xl">{stats.totalDistance.toFixed(2)} km</CardTitle>
          </CardHeader>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Time</CardDescription>
            <CardTitle className="text-2xl">{stats.totalTime}</CardTitle>
          </CardHeader>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Avg Pace</CardDescription>
            <CardTitle className="text-2xl">{formatPace(stats.averagePace)}</CardTitle>
          </CardHeader>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Elevation</CardDescription>
            <CardTitle className="text-2xl">{stats.elevationGain} m</CardTitle>
          </CardHeader>
        </Card>
        
        {showHeartRate && stats.averageHeartRate && (
          <>
            <Card>
              <CardHeader className="pb-2">
                <CardDescription>Avg HR</CardDescription>
                <CardTitle className="text-2xl">{stats.averageHeartRate} bpm</CardTitle>
              </CardHeader>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardDescription>Max HR</CardDescription>
                <CardTitle className="text-2xl">{stats.maxHeartRate} bpm</CardTitle>
              </CardHeader>
            </Card>
          </>
        )}
      </div>

      {/* Pace and Speed Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Pace & Speed</CardTitle>
          <CardDescription>
            Your pacing throughout the activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                yAxisId="pace"
                orientation="left"
                tick={{ fontSize: 12 }}
                label={{ value: 'Pace (min/km)', angle: -90, position: 'insideLeft' }}
              />
              <YAxis 
                yAxisId="speed"
                orientation="right"
                tick={{ fontSize: 12 }}
                label={{ value: 'Speed (km/h)', angle: 90, position: 'insideRight' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                yAxisId="pace"
                type="monotone"
                dataKey="pace"
                stroke="#8884d8"
                strokeWidth={2}
                dot={false}
                name="Pace"
              />
              <Line
                yAxisId="speed"
                type="monotone"
                dataKey="speed"
                stroke="#82ca9d"
                strokeWidth={2}
                dot={false}
                name="Speed"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Elevation Profile */}
      <Card>
        <CardHeader>
          <CardTitle>Elevation Profile</CardTitle>
          <CardDescription>
            Elevation changes throughout your route
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={250}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                label={{ value: 'Elevation (m)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="elevation"
                stroke="#ffc658"
                fill="#ffc658"
                fillOpacity={0.6}
                name="Elevation"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Heart Rate Chart (if enabled) */}
      {showHeartRate && (
        <Card>
          <CardHeader>
            <CardTitle>Heart Rate</CardTitle>
            <CardDescription>
              Heart rate zones and variability
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="time" 
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis 
                  yAxisId="hr"
                  orientation="left"
                  tick={{ fontSize: 12 }}
                  label={{ value: 'Heart Rate (bpm)', angle: -90, position: 'insideLeft' }}
                />
                <YAxis 
                  yAxisId="hrv"
                  orientation="right"
                  tick={{ fontSize: 12 }}
                  label={{ value: 'HRV (ms)', angle: 90, position: 'insideRight' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area
                  yAxisId="hr"
                  type="monotone"
                  dataKey="heartRate"
                  stroke="#ff7300"
                  fill="#ff7300"
                  fillOpacity={0.3}
                  name="Heart Rate"
                />
                {data[0]?.hrv && (
                  <Line
                    yAxisId="hrv"
                    type="monotone"
                    dataKey="hrv"
                    stroke="#00ff00"
                    strokeWidth={1}
                    dot={false}
                    name="HRV"
                  />
                )}
              </ComposedChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Activity Badges */}
      <div className="flex flex-wrap gap-2">
        <Badge variant="secondary">{activityType}</Badge>
        <Badge variant="outline">
          {stats.averageSpeed.toFixed(1)} km/h avg speed
        </Badge>
        {showHeartRate && stats.averageHRV && (
          <Badge variant="outline">
            {stats.averageHRV} ms avg HRV
          </Badge>
        )}
        <Badge variant="outline">
          {data.length} data points
        </Badge>
      </div>
    </div>
  );
}
