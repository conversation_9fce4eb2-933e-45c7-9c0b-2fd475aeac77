import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { addTokenTransaction, getTokenBalance } from '@/lib/database';

// Token packages configuration
const TOKEN_PACKAGES = {
  starter: { tokens: 10, price: 4.99, bonus: 0 },
  popular: { tokens: 25, price: 9.99, bonus: 5 },
  pro: { tokens: 50, price: 17.99, bonus: 15 },
  unlimited: { tokens: 100, price: 29.99, bonus: 30 },
};

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const body = await request.json();
    const { packageId, tokens, amount } = body;

    // Validate input
    if (!packageId || !tokens || !amount) {
      return NextResponse.json(
        { error: 'Missing required fields: packageId, tokens, amount' },
        { status: 400 }
      );
    }

    // Validate package
    const packageInfo = TOKEN_PACKAGES[packageId as keyof typeof TOKEN_PACKAGES];
    if (!packageInfo) {
      return NextResponse.json(
        { error: 'Invalid package ID' },
        { status: 400 }
      );
    }

    // Verify the token count and amount match the package
    const expectedTokens = packageInfo.tokens + packageInfo.bonus;
    if (tokens !== expectedTokens || Math.abs(amount - packageInfo.price) > 0.01) {
      return NextResponse.json(
        { error: 'Package details do not match' },
        { status: 400 }
      );
    }

    // In a real application, you would:
    // 1. Process payment with Stripe or another payment processor
    // 2. Verify the payment was successful
    // 3. Only then add tokens to the user's account

    // For demo purposes, we'll simulate a successful payment
    // In production, replace this with actual payment processing:
    /*
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'usd',
      metadata: {
        userId: user.id,
        packageId,
        tokens: tokens.toString(),
      },
    });

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment failed' },
        { status: 402 }
      );
    }
    */

    // Add tokens to user's account
    await addTokenTransaction(
      user.id,
      'purchase',
      tokens,
      `Token purchase: ${packageId} package (${packageInfo.tokens} + ${packageInfo.bonus} bonus)`
    );

    // Get updated balance
    const newBalance = await getTokenBalance(user.id);

    return NextResponse.json({
      success: true,
      tokensAdded: tokens,
      newBalance,
      packageId,
      transactionId: `demo_${Date.now()}`, // In production, use actual payment transaction ID
    });

  } catch (error) {
    console.error('Token purchase error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;

    // Get current token balance
    const balance = await getTokenBalance(user.id);

    return NextResponse.json({
      balance,
      packages: Object.entries(TOKEN_PACKAGES).map(([id, pkg]) => ({
        id,
        ...pkg,
        totalTokens: pkg.tokens + pkg.bonus,
        pricePerToken: (pkg.price / (pkg.tokens + pkg.bonus)).toFixed(3),
      })),
    });

  } catch (error) {
    console.error('Get token info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
