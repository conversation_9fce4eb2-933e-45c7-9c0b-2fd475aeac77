import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { useTokensForExport } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const body = await request.json();
    const { activityName, activityType, tokensRequired = 1 } = body;

    // Validate input
    if (!activityName || !activityType) {
      return NextResponse.json(
        { error: 'Missing required fields: activityName, activityType' },
        { status: 400 }
      );
    }

    if (tokensRequired < 1 || tokensRequired > 10) {
      return NextResponse.json(
        { error: 'Invalid token amount. Must be between 1 and 10.' },
        { status: 400 }
      );
    }

    // Use tokens for export
    const result = await useTokensForExport(
      user.id,
      activityName,
      activityType,
      tokensRequired
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 402 } // Payment Required
      );
    }

    return NextResponse.json({
      success: true,
      tokensUsed: tokensRequired,
      newBalance: result.newBalance,
      activityName,
      activityType,
    });

  } catch (error) {
    console.error('Token usage error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
