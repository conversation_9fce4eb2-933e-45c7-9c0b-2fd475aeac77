'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';

import { useWaypointStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, Download, MapPin, Clock, Zap, Mountain, Heart, Settings, 
  Coins, Eye, User, LogIn, Calendar, Search, Trash2, Navigation,
  Route, Activity
} from 'lucide-react';
import { toast } from 'sonner';
import { GPXGenerationOptions, downloadGpx, generateGpx } from '@/lib/gpx';
import DataVisualization, { DataPoint, VisualizationStats } from './DataVisualization';
import TokenPurchase from './TokenPurchase';
import AuthForm from './AuthForm';

// Dynamic import for the map component to avoid SSR issues
const DynamicMap = dynamic(() => import('./InteractiveMap'), {
  ssr: false,
  loading: () => <div className="h-[400px] bg-gray-100 rounded-lg flex items-center justify-center">Loading map...</div>
});



interface GPXFormData {
  name: string;
  description: string;
  activityType: 'Run' | 'Bike' | 'Walk';
  inputType: 'speed' | 'pace';
  averageSpeedKmh: number;
  averagePaceMinPerKm: number;
  routingProfile: 'foot-walking' | 'cycling-regular' | 'driving-car';
  addNoise: boolean;
  pauseDuration: number;
  useRealisticTiming: boolean;
  samplingRateSeconds: number;
  speedVariation: number;
  // Heart rate options
  includeHeartRate: boolean;
  userAge: number;
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced';
  averageHeartRate?: number;
  includeHRV: boolean;
  // Enhanced pace inconsistency
  paceInconsistency: 'low' | 'medium' | 'high';
  includeRestStops: boolean;
  fatigueEffect: boolean;
  // Start time
  startDate: string;
  startTime: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  tokenBalance: number;
}



export default function UnifiedRouteGPXBuilder() {
  const { waypoints, routeGeometry, totalDistance, addWaypoint, removeWaypoint, clearWaypoints, setRouteGeometry } = useWaypointStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRouting, setIsRouting] = useState(false);
  const [previewData, setPreviewData] = useState<DataPoint[] | null>(null);
  const [previewStats, setPreviewStats] = useState<VisualizationStats | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showTokenPurchase, setShowTokenPurchase] = useState(false);
  const [showAuth, setShowAuth] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [user, setUser] = useState<User | null>(null);

  const [formData, setFormData] = useState<GPXFormData>({
    name: 'My Activity',
    description: 'Generated with realistic timing patterns and heart rate data',
    activityType: 'Run',
    inputType: 'speed',
    averageSpeedKmh: 12,
    averagePaceMinPerKm: 5,
    routingProfile: 'foot-walking',
    addNoise: true,
    pauseDuration: 0,
    useRealisticTiming: true,
    samplingRateSeconds: 4,
    speedVariation: 0.15,
    // Heart rate options
    includeHeartRate: true,
    userAge: 35,
    fitnessLevel: 'intermediate',
    averageHeartRate: undefined,
    includeHRV: true,
    // Enhanced pace inconsistency
    paceInconsistency: 'medium',
    includeRestStops: false,
    fatigueEffect: false,
    // Start time
    startDate: new Date().toISOString().split('T')[0],
    startTime: '08:00',
  });

  // Check for existing authentication on component mount
  useEffect(() => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      fetch('/api/auth/me', {
        headers: { 'Authorization': `Bearer ${token}` },
      })
      .then(res => res.json())
      .then(data => {
        if (data.user) setUser(data.user);
      })
      .catch(() => {
        localStorage.removeItem('auth-token');
        document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      });
    }
  }, []);

  // Update routing profile when activity type changes
  useEffect(() => {
    const profileMap = {
      'Run': 'foot-walking',
      'Walk': 'foot-walking',
      'Bike': 'cycling-regular',
    } as const;
    
    setFormData(prev => ({
      ...prev,
      routingProfile: profileMap[prev.activityType]
    }));
  }, [formData.activityType]);

  const handleInputChange = (field: keyof GPXFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Auto-convert between speed and pace
      if (field === 'inputType') {
        if (value === 'pace' && prev.averageSpeedKmh) {
          newData.averagePaceMinPerKm = Math.round((60 / prev.averageSpeedKmh) * 10) / 10;
        } else if (value === 'speed' && prev.averagePaceMinPerKm) {
          newData.averageSpeedKmh = Math.round((60 / prev.averagePaceMinPerKm) * 10) / 10;
        }
      } else if (field === 'averageSpeedKmh' && prev.inputType === 'speed') {
        newData.averagePaceMinPerKm = Math.round((60 / (value as number)) * 10) / 10;
      } else if (field === 'averagePaceMinPerKm' && prev.inputType === 'pace') {
        newData.averageSpeedKmh = Math.round((60 / (value as number)) * 10) / 10;
      }
      
      return newData;
    });
  };

  const handleMapClick = async (lat: number, lng: number) => {
    addWaypoint({ lat, lng });

    if (waypoints.length >= 1) {
      await generateRoute([...waypoints, { lat, lng }]);
    }
  };

  const generateRoute = async (points: Array<{ lat: number; lng: number }>) => {
    if (points.length < 2) return;

    setIsRouting(true);
    try {
      const coordinates = points.map(p => `${p.lng},${p.lat}`).join(';');
      const response = await fetch(
        `https://api.openrouteservice.org/v2/directions/${formData.routingProfile}?api_key=5b3ce3597851110001cf6248d5a1b9b8a8b84b8bb5e8b8e8b8e8b8e8&start=${points[0].lng},${points[0].lat}&end=${points[points.length - 1].lng},${points[points.length - 1].lat}`
      );

      if (!response.ok) {
        throw new Error('Routing failed');
      }

      const data = await response.json();
      const routeCoordinates = data.features[0].geometry.coordinates.map((coord: [number, number]) => [coord[1], coord[0]]);

      setRouteGeometry(routeCoordinates);
      toast.success('Route generated successfully!');
    } catch (error) {
      console.error('Routing error:', error);
      toast.error('Failed to generate route. Please try again.');
    } finally {
      setIsRouting(false);
    }
  };

  const handleCitySelect = (lat: number, lng: number, name: string) => {
    // City selection just centers the map, doesn't add waypoints
    toast.success(`Map centered on ${name}`);
  };

  const generatePreview = async () => {
    if (!routeGeometry || routeGeometry.length < 2) {
      toast.error('Please create a route first');
      return;
    }

    setIsGenerating(true);
    try {
      const coordinates: [number, number][] = routeGeometry.map(point => [point[1], point[0]]);

      const options: GPXGenerationOptions = {
        name: formData.name,
        description: formData.description,
        activityType: formData.activityType,
        coordinates,
        startTime: new Date(`${formData.startDate}T${formData.startTime}`),
        averageSpeedKmh: formData.inputType === 'speed' ? formData.averageSpeedKmh : undefined,
        averagePaceMinPerKm: formData.inputType === 'pace' ? formData.averagePaceMinPerKm : undefined,
        addNoise: formData.addNoise,
        pauseDuration: formData.pauseDuration,
        useRealisticTiming: formData.useRealisticTiming,
        samplingRateSeconds: formData.samplingRateSeconds,
        speedVariation: formData.speedVariation,
        addElevation: true, // Always use elevation
        useRealElevation: true, // Always use real elevation API
        useElevationAdjustedPacing: true,
        includeHeartRate: formData.includeHeartRate,
        userAge: formData.userAge,
        fitnessLevel: formData.fitnessLevel,
        averageHeartRate: formData.averageHeartRate,
        includeHRV: formData.includeHRV,
        paceInconsistency: formData.paceInconsistency,
        includeRestStops: formData.includeRestStops,
        fatigueEffect: formData.fatigueEffect,
      };

      const gpxContent = await generateGpx(options);

      // Parse GPX to extract data points for visualization
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(gpxContent, 'text/xml');
      const trackPoints = xmlDoc.querySelectorAll('trkpt');

      const dataPoints: DataPoint[] = Array.from(trackPoints).map((point, index) => {
        const lat = parseFloat(point.getAttribute('lat') || '0');
        const lng = parseFloat(point.getAttribute('lon') || '0');
        const eleElement = point.querySelector('ele');
        const timeElement = point.querySelector('time');
        const hrElement = point.querySelector('gpxtpx\\:hr, hr');
        const speedElement = point.querySelector('gpxtpx\\:speed, speed');

        const elevation = eleElement ? parseFloat(eleElement.textContent || '0') : 0;
        const heartRate = hrElement ? parseInt(hrElement.textContent || '0') : undefined;
        const speed = speedElement ? parseFloat(speedElement.textContent || '0') : formData.averageSpeedKmh;
        const pace = speed > 0 ? 60 / speed : 0;

        return {
          distance: index * 0.1, // Approximate distance
          elevation,
          pace,
          speed,
          heartRate,
          time: timeElement ? new Date(timeElement.textContent || '') : new Date(),
        };
      });

      setPreviewData(dataPoints);

      // Calculate stats
      const totalTime = dataPoints.length > 0 ?
        (dataPoints[dataPoints.length - 1].time.getTime() - dataPoints[0].time.getTime()) / 1000 : 0;
      const avgPace = dataPoints.reduce((sum, p) => sum + p.pace, 0) / dataPoints.length;
      const avgSpeed = dataPoints.reduce((sum, p) => sum + p.speed, 0) / dataPoints.length;
      const avgHR = formData.includeHeartRate ?
        dataPoints.reduce((sum, p) => sum + (p.heartRate || 0), 0) / dataPoints.length : undefined;
      const elevationGain = Math.max(...dataPoints.map(p => p.elevation)) - Math.min(...dataPoints.map(p => p.elevation));

      setPreviewStats({
        totalDistance,
        totalTime,
        averagePace: avgPace,
        averageSpeed: avgSpeed,
        elevationGain,
        averageHeartRate: avgHR,
      });

      setShowPreview(true);
      toast.success('Preview generated successfully!');
    } catch (error) {
      console.error('Preview generation error:', error);
      toast.error('Failed to generate preview. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!user) {
      setShowAuth(true);
      return;
    }

    if (user.tokenBalance < 1) {
      setShowTokenPurchase(true);
      return;
    }

    if (!routeGeometry || routeGeometry.length < 2) {
      toast.error('Please create a route first');
      return;
    }

    setIsGenerating(true);
    try {
      const coordinates: [number, number][] = routeGeometry.map(point => [point[1], point[0]]);

      const options: GPXGenerationOptions = {
        name: formData.name,
        description: formData.description,
        activityType: formData.activityType,
        coordinates,
        startTime: new Date(`${formData.startDate}T${formData.startTime}`),
        averageSpeedKmh: formData.inputType === 'speed' ? formData.averageSpeedKmh : undefined,
        averagePaceMinPerKm: formData.inputType === 'pace' ? formData.averagePaceMinPerKm : undefined,
        addNoise: formData.addNoise,
        pauseDuration: formData.pauseDuration,
        useRealisticTiming: formData.useRealisticTiming,
        samplingRateSeconds: formData.samplingRateSeconds,
        speedVariation: formData.speedVariation,
        addElevation: true, // Always use elevation
        useRealElevation: true, // Always use real elevation API
        useElevationAdjustedPacing: true,
        includeHeartRate: formData.includeHeartRate,
        userAge: formData.userAge,
        fitnessLevel: formData.fitnessLevel,
        averageHeartRate: formData.averageHeartRate,
        includeHRV: formData.includeHRV,
        paceInconsistency: formData.paceInconsistency,
        includeRestStops: formData.includeRestStops,
        fatigueEffect: formData.fatigueEffect,
      };

      await downloadGpx(options);

      // Deduct token
      const token = localStorage.getItem('auth-token');
      await fetch('/api/tokens/deduct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ amount: 1, description: `GPX export: ${formData.name}` }),
      });

      // Update user balance
      setUser(prev => prev ? { ...prev, tokenBalance: prev.tokenBalance - 1 } : null);

      toast.success('GPX file downloaded successfully!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download GPX file. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAuthSuccess = (userData: User) => {
    setUser(userData);
    setShowAuth(false);
    toast.success('Successfully logged in!');
  };

  const handleTokenPurchaseSuccess = (newBalance: number) => {
    setUser(prev => prev ? { ...prev, tokenBalance: newBalance } : null);
    setShowTokenPurchase(false);
    toast.success('Tokens purchased successfully!');
  };

  const handleLogout = () => {
    localStorage.removeItem('auth-token');
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    setUser(null);
    toast.success('Logged out successfully');
  };



  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
      {/* Map Section */}
      <Card className="h-fit">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Interactive Route Builder
          </CardTitle>
          <CardDescription>
            Click on the map to create waypoints. Routes are automatically generated based on activity type.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Interactive Map */}
            <DynamicMap
              onMapClick={handleMapClick}
              route={routeGeometry ? routeGeometry.map(([lat, lng]) => ({ lat, lng })) : []}
              className="h-[400px] rounded-lg"
            />

            {/* Route Info */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Badge variant="outline" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {waypoints.length} waypoints
                </Badge>
                {totalDistance > 0 && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Route className="h-3 w-3" />
                    {totalDistance.toFixed(2)} km
                  </Badge>
                )}
                {isRouting && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Routing...
                  </Badge>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={clearWaypoints}
                disabled={waypoints.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Panel */}
      <div className="space-y-6">
        {/* User Authentication Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Account
            </CardTitle>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Welcome, {user.name}</span>
                  <Button variant="ghost" size="sm" onClick={handleLogout}>
                    Logout
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Coins className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">{user.tokenBalance} tokens</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowTokenPurchase(true)}
                  >
                    Buy Tokens
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Sign in to export GPX files
                </p>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setAuthMode('login');
                      setShowAuth(true);
                    }}
                  >
                    <LogIn className="h-4 w-4 mr-1" />
                    Login
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setAuthMode('register');
                      setShowAuth(true);
                    }}
                  >
                    <User className="h-4 w-4 mr-1" />
                    Register
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Activity Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Activity Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Activity Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="My Activity"
                />
              </div>
              <div>
                <Label htmlFor="activityType">Activity Type</Label>
                <Select
                  value={formData.activityType}
                  onValueChange={(value) => handleInputChange('activityType', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Run">🏃‍♂️ Running</SelectItem>
                    <SelectItem value="Bike">🚴‍♂️ Cycling</SelectItem>
                    <SelectItem value="Walk">🚶‍♂️ Walking</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Activity description"
              />
            </div>

            {/* Speed/Pace Input */}
            <div className="space-y-3">
              <div className="flex items-center gap-4">
                <Label>Input Type:</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="speed"
                    checked={formData.inputType === 'speed'}
                    onCheckedChange={() => handleInputChange('inputType', 'speed')}
                  />
                  <Label htmlFor="speed" className="text-sm">Speed (km/h)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="pace"
                    checked={formData.inputType === 'pace'}
                    onCheckedChange={() => handleInputChange('inputType', 'pace')}
                  />
                  <Label htmlFor="pace" className="text-sm">Pace (min/km)</Label>
                </div>
              </div>

              {formData.inputType === 'speed' ? (
                <div>
                  <Label htmlFor="speed-input">Average Speed (km/h)</Label>
                  <Input
                    id="speed-input"
                    type="number"
                    step="0.1"
                    value={formData.averageSpeedKmh}
                    onChange={(e) => handleInputChange('averageSpeedKmh', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Equivalent pace: {formData.averagePaceMinPerKm.toFixed(1)} min/km
                  </p>
                </div>
              ) : (
                <div>
                  <Label htmlFor="pace-input">Average Pace (min/km)</Label>
                  <Input
                    id="pace-input"
                    type="number"
                    step="0.1"
                    value={formData.averagePaceMinPerKm}
                    onChange={(e) => handleInputChange('averagePaceMinPerKm', parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Equivalent speed: {formData.averageSpeedKmh.toFixed(1)} km/h
                  </p>
                </div>
              )}
            </div>

            {/* Start Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Advanced Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Heart Rate Settings */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeHeartRate"
                  checked={formData.includeHeartRate}
                  onCheckedChange={(checked) => handleInputChange('includeHeartRate', checked)}
                />
                <Label htmlFor="includeHeartRate" className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-red-500" />
                  Include Heart Rate Data
                </Label>
              </div>

              {formData.includeHeartRate && (
                <div className="ml-6 space-y-3 border-l-2 border-gray-200 pl-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="userAge">Age</Label>
                      <Input
                        id="userAge"
                        type="number"
                        value={formData.userAge}
                        onChange={(e) => handleInputChange('userAge', parseInt(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="fitnessLevel">Fitness Level</Label>
                      <Select
                        value={formData.fitnessLevel}
                        onValueChange={(value) => handleInputChange('fitnessLevel', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="beginner">Beginner</SelectItem>
                          <SelectItem value="intermediate">Intermediate</SelectItem>
                          <SelectItem value="advanced">Advanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeHRV"
                      checked={formData.includeHRV}
                      onCheckedChange={(checked) => handleInputChange('includeHRV', checked)}
                    />
                    <Label htmlFor="includeHRV" className="text-sm">Include Heart Rate Variability</Label>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Pace Inconsistency */}
            <div className="space-y-3">
              <Label>Pace Inconsistency Level</Label>
              <Select
                value={formData.paceInconsistency}
                onValueChange={(value) => handleInputChange('paceInconsistency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low (±5%)</SelectItem>
                  <SelectItem value="medium">Medium (±15%)</SelectItem>
                  <SelectItem value="high">High (±25%)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Additional Options */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRestStops"
                  checked={formData.includeRestStops}
                  onCheckedChange={(checked) => handleInputChange('includeRestStops', checked)}
                />
                <Label htmlFor="includeRestStops" className="text-sm">Include Rest Stops</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="fatigueEffect"
                  checked={formData.fatigueEffect}
                  onCheckedChange={(checked) => handleInputChange('fatigueEffect', checked)}
                />
                <Label htmlFor="fatigueEffect" className="text-sm">Gradual Fatigue Effect</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="useRealisticTiming"
                  checked={formData.useRealisticTiming}
                  onCheckedChange={(checked) => handleInputChange('useRealisticTiming', checked)}
                />
                <Label htmlFor="useRealisticTiming" className="text-sm">Realistic Timing Patterns</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={generatePreview}
            disabled={!routeGeometry || routeGeometry.length < 2 || isGenerating}
            className="w-full"
            variant="outline"
          >
            {isGenerating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Eye className="h-4 w-4 mr-2" />
            )}
            Generate Preview
          </Button>

          <Button
            onClick={handleDownload}
            disabled={!routeGeometry || routeGeometry.length < 2 || isGenerating}
            className="w-full"
          >
            {isGenerating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Download GPX {user && `(1 token)`}
          </Button>

          {!user && (
            <Alert>
              <AlertDescription>
                Sign in to download GPX files. New users get 5 free tokens!
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>

      {/* Modals */}
      {showPreview && previewData && previewStats && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Activity Preview</h2>
                <Button variant="ghost" onClick={() => setShowPreview(false)}>
                  ×
                </Button>
              </div>
              <DataVisualization data={previewData} stats={previewStats} />
            </div>
          </div>
        </div>
      )}

      {showAuth && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <AuthForm
              mode={authMode}
              onSuccess={handleAuthSuccess}
              onClose={() => setShowAuth(false)}
            />
          </div>
        </div>
      )}

      {showTokenPurchase && user && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <TokenPurchase
              currentBalance={user.tokenBalance}
              onSuccess={handleTokenPurchaseSuccess}
              onClose={() => setShowTokenPurchase(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
