'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Coins, CreditCard, Check } from 'lucide-react';
import { toast } from 'sonner';

interface TokenPackage {
  id: string;
  tokens: number;
  price: number;
  popular?: boolean;
  bonus?: number;
}

const TOKEN_PACKAGES: TokenPackage[] = [
  {
    id: 'starter',
    tokens: 10,
    price: 4.99,
  },
  {
    id: 'popular',
    tokens: 25,
    price: 9.99,
    popular: true,
    bonus: 5,
  },
  {
    id: 'pro',
    tokens: 50,
    price: 17.99,
    bonus: 15,
  },
  {
    id: 'unlimited',
    tokens: 100,
    price: 29.99,
    bonus: 30,
  },
];

interface TokenPurchaseProps {
  currentBalance: number;
  onPurchaseSuccess?: (newBalance: number) => void;
  onClose?: () => void;
}

export default function TokenPurchase({ 
  currentBalance, 
  onPurchaseSuccess, 
  onClose 
}: TokenPurchaseProps) {
  const [selectedPackage, setSelectedPackage] = useState<TokenPackage | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePurchase = async (pkg: TokenPackage) => {
    setIsProcessing(true);
    setError(null);
    setSelectedPackage(pkg);

    try {
      // In a real app, this would integrate with Stripe or another payment processor
      const response = await fetch('/api/tokens/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
        },
        body: JSON.stringify({
          packageId: pkg.id,
          tokens: pkg.tokens + (pkg.bonus || 0),
          amount: pkg.price,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Purchase failed');
      }

      toast.success('Tokens purchased successfully!', {
        description: `Added ${pkg.tokens + (pkg.bonus || 0)} tokens to your account`,
      });

      onPurchaseSuccess?.(result.newBalance);
      onClose?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Purchase failed';
      setError(errorMessage);
      toast.error('Purchase failed', {
        description: errorMessage,
      });
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  const formatPrice = (price: number) => `$${price.toFixed(2)}`;
  const calculatePricePerToken = (price: number, tokens: number, bonus: number = 0) => 
    (price / (tokens + bonus)).toFixed(3);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Purchase Tokens</h2>
        <p className="text-gray-600 mt-2">
          Each GPX export costs 1 token. Choose a package that fits your needs.
        </p>
        <div className="flex items-center justify-center gap-2 mt-4">
          <Coins className="h-5 w-5 text-yellow-500" />
          <span className="text-lg font-semibold">
            Current Balance: {currentBalance} tokens
          </span>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {TOKEN_PACKAGES.map((pkg) => {
          const totalTokens = pkg.tokens + (pkg.bonus || 0);
          const isSelected = selectedPackage?.id === pkg.id;
          
          return (
            <Card 
              key={pkg.id} 
              className={`relative transition-all duration-200 hover:shadow-lg ${
                pkg.popular ? 'ring-2 ring-blue-500' : ''
              } ${isSelected ? 'ring-2 ring-green-500' : ''}`}
            >
              {pkg.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">Most Popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-lg">
                  {pkg.tokens} Tokens
                  {pkg.bonus && (
                    <span className="text-green-600 text-sm font-normal">
                      {' '}+ {pkg.bonus} bonus
                    </span>
                  )}
                </CardTitle>
                <div className="text-3xl font-bold text-blue-600">
                  {formatPrice(pkg.price)}
                </div>
                <CardDescription>
                  ${calculatePricePerToken(pkg.price, pkg.tokens, pkg.bonus)} per token
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>{pkg.tokens} base tokens</span>
                  </div>
                  
                  {pkg.bonus && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Check className="h-4 w-4 text-green-500" />
                      <span>{pkg.bonus} bonus tokens</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>No expiration</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>Instant delivery</span>
                  </div>
                </div>
                
                <Button
                  className="w-full mt-6"
                  onClick={() => handlePurchase(pkg)}
                  disabled={isProcessing}
                  variant={pkg.popular ? 'default' : 'outline'}
                >
                  {isProcessing && isSelected ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Buy {totalTokens} Tokens
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 mb-2">Why buy tokens?</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Generate unlimited realistic GPX files</li>
          <li>• Include heart rate and elevation data</li>
          <li>• Support continued development</li>
          <li>• No subscription required</li>
        </ul>
      </div>

      <div className="text-center">
        <p className="text-xs text-gray-500">
          Secure payment processing powered by Stripe. 
          Your payment information is never stored on our servers.
        </p>
      </div>
    </div>
  );
}
