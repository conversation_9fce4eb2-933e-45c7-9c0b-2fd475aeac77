/**
 * Database Library
 * Simple SQLite database for user management and token tracking
 */

import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import { User, AuthUser } from './auth';

// Database file path
const DB_PATH = path.join(process.cwd(), 'data', 'app.db');

// Create database connection
let db: sqlite3.Database | null = null;

/**
 * Initialize database connection and create tables
 */
export async function initDatabase(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Ensure data directory exists
    const fs = require('fs');
    const dataDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        reject(err);
        return;
      }

      console.log('Connected to SQLite database');
      createTables().then(resolve).catch(reject);
    });
  });
}

/**
 * Create database tables
 */
async function createTables(): Promise<void> {
  if (!db) throw new Error('Database not initialized');

  const run = promisify(db.run.bind(db));

  // Users table
  await run(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      password_hash TEXT NOT NULL,
      token_balance INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login DATETIME,
      is_active BOOLEAN DEFAULT 1
    )
  `);

  // Token transactions table
  await run(`
    CREATE TABLE IF NOT EXISTS token_transactions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      type TEXT NOT NULL, -- 'purchase', 'usage', 'refund'
      amount INTEGER NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // GPX exports table
  await run(`
    CREATE TABLE IF NOT EXISTS gpx_exports (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      activity_name TEXT NOT NULL,
      activity_type TEXT NOT NULL,
      tokens_used INTEGER NOT NULL,
      file_size INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Create indexes
  await run('CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)');
  await run('CREATE INDEX IF NOT EXISTS idx_token_transactions_user_id ON token_transactions (user_id)');
  await run('CREATE INDEX IF NOT EXISTS idx_gpx_exports_user_id ON gpx_exports (user_id)');

  console.log('Database tables created successfully');
}

/**
 * Get database connection
 */
function getDb(): sqlite3.Database {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }
  return db;
}

/**
 * Generate a unique ID
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Create a new user
 */
export async function createUser(
  email: string,
  name: string,
  passwordHash: string,
  initialTokens: number = 5
): Promise<User> {
  const database = getDb();
  const run = promisify(database.run.bind(database));

  const userId = generateId();
  const now = new Date().toISOString();

  await run(
    `INSERT INTO users (id, email, name, password_hash, token_balance, created_at) 
     VALUES (?, ?, ?, ?, ?, ?)`,
    [userId, email, name, passwordHash, initialTokens, now]
  );

  // Record initial token grant
  if (initialTokens > 0) {
    await addTokenTransaction(userId, 'purchase', initialTokens, 'Welcome bonus');
  }

  return {
    id: userId,
    email,
    name,
    tokenBalance: initialTokens,
    createdAt: new Date(now),
  };
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<(User & { passwordHash: string }) | null> {
  const database = getDb();
  const get = promisify(database.get.bind(database));

  const row = await get(
    'SELECT * FROM users WHERE email = ? AND is_active = 1',
    [email]
  ) as any;

  if (!row) return null;

  return {
    id: row.id,
    email: row.email,
    name: row.name,
    passwordHash: row.password_hash,
    tokenBalance: row.token_balance,
    createdAt: new Date(row.created_at),
    lastLogin: row.last_login ? new Date(row.last_login) : undefined,
  };
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string): Promise<AuthUser | null> {
  const database = getDb();
  const get = promisify(database.get.bind(database));

  const row = await get(
    'SELECT id, email, name, token_balance, created_at, last_login FROM users WHERE id = ? AND is_active = 1',
    [userId]
  ) as any;

  if (!row) return null;

  return {
    id: row.id,
    email: row.email,
    name: row.name,
    tokenBalance: row.token_balance,
    createdAt: new Date(row.created_at),
    lastLogin: row.last_login ? new Date(row.last_login) : undefined,
  };
}

/**
 * Update user's last login time
 */
export async function updateLastLogin(userId: string): Promise<void> {
  const database = getDb();
  const run = promisify(database.run.bind(database));

  await run(
    'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
    [userId]
  );
}

/**
 * Add token transaction
 */
export async function addTokenTransaction(
  userId: string,
  type: 'purchase' | 'usage' | 'refund',
  amount: number,
  description?: string
): Promise<void> {
  const database = getDb();
  const run = promisify(database.run.bind(database));

  const transactionId = generateId();

  await run(
    `INSERT INTO token_transactions (id, user_id, type, amount, description) 
     VALUES (?, ?, ?, ?, ?)`,
    [transactionId, userId, type, amount, description]
  );

  // Update user's token balance
  const balanceChange = type === 'usage' ? -amount : amount;
  await run(
    'UPDATE users SET token_balance = token_balance + ? WHERE id = ?',
    [balanceChange, userId]
  );
}

/**
 * Get user's token balance
 */
export async function getTokenBalance(userId: string): Promise<number> {
  const database = getDb();
  const get = promisify(database.get.bind(database));

  const row = await get(
    'SELECT token_balance FROM users WHERE id = ?',
    [userId]
  ) as any;

  return row ? row.token_balance : 0;
}

/**
 * Use tokens for GPX export
 */
export async function useTokensForExport(
  userId: string,
  activityName: string,
  activityType: string,
  tokensRequired: number = 1
): Promise<{ success: boolean; newBalance?: number; error?: string }> {
  const database = getDb();
  const run = promisify(database.run.bind(database));

  // Check current balance
  const currentBalance = await getTokenBalance(userId);
  
  if (currentBalance < tokensRequired) {
    return {
      success: false,
      error: `Insufficient tokens. You have ${currentBalance}, but need ${tokensRequired}.`
    };
  }

  // Record the export
  const exportId = generateId();
  await run(
    `INSERT INTO gpx_exports (id, user_id, activity_name, activity_type, tokens_used) 
     VALUES (?, ?, ?, ?, ?)`,
    [exportId, userId, activityName, activityType, tokensRequired]
  );

  // Deduct tokens
  await addTokenTransaction(userId, 'usage', tokensRequired, `GPX export: ${activityName}`);

  const newBalance = currentBalance - tokensRequired;
  return {
    success: true,
    newBalance
  };
}

/**
 * Get user's export history
 */
export async function getExportHistory(userId: string, limit: number = 50): Promise<any[]> {
  const database = getDb();
  const all = promisify(database.all.bind(database));

  const rows = await all(
    `SELECT * FROM gpx_exports 
     WHERE user_id = ? 
     ORDER BY created_at DESC 
     LIMIT ?`,
    [userId, limit]
  ) as any[];

  return rows.map(row => ({
    id: row.id,
    activityName: row.activity_name,
    activityType: row.activity_type,
    tokensUsed: row.tokens_used,
    fileSize: row.file_size,
    createdAt: new Date(row.created_at),
  }));
}

/**
 * Get user's token transaction history
 */
export async function getTokenHistory(userId: string, limit: number = 50): Promise<any[]> {
  const database = getDb();
  const all = promisify(database.all.bind(database));

  const rows = await all(
    `SELECT * FROM token_transactions 
     WHERE user_id = ? 
     ORDER BY created_at DESC 
     LIMIT ?`,
    [userId, limit]
  ) as any[];

  return rows.map(row => ({
    id: row.id,
    type: row.type,
    amount: row.amount,
    description: row.description,
    createdAt: new Date(row.created_at),
  }));
}

/**
 * Close database connection
 */
export async function closeDatabase(): Promise<void> {
  if (db) {
    return new Promise((resolve, reject) => {
      db!.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Database connection closed');
          db = null;
          resolve();
        }
      });
    });
  }
}

// Initialize database on module load (server-side only)
if (typeof window === 'undefined') {
  initDatabase().catch(console.error);
}
