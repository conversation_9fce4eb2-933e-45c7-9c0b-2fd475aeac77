/**
 * Heart Rate Simulation Library
 * Generates realistic heart rate data for GPX activities
 */

export interface HeartRateOptions {
  activityType: 'Run' | 'Bike' | 'Walk';
  duration: number; // in seconds
  userAge?: number; // for calculating max HR
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced';
  averageHeartRate?: number; // override automatic calculation
  includeHRV?: boolean; // heart rate variability
}

export interface HeartRateData {
  timestamp: Date;
  heartRate: number;
  hrv?: number; // heart rate variability in ms
}

export interface HeartRateStats {
  averageHR: number;
  maxHR: number;
  minHR: number;
  averageHRV?: number;
  zones: {
    zone1: number; // % time in zone 1 (50-60% max HR)
    zone2: number; // % time in zone 2 (60-70% max HR)
    zone3: number; // % time in zone 3 (70-80% max HR)
    zone4: number; // % time in zone 4 (80-90% max HR)
    zone5: number; // % time in zone 5 (90-100% max HR)
  };
}

/**
 * Calculate maximum heart rate based on age
 */
function calculateMaxHeartRate(age: number): number {
  // Using the more accurate Tanaka formula: 208 - (0.7 × age)
  return Math.round(208 - (0.7 * age));
}

/**
 * Get target heart rate zones based on activity and fitness level
 */
function getTargetHeartRateZones(
  maxHR: number,
  activityType: 'Run' | 'Bike' | 'Walk',
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced'
): { min: number; max: number; target: number } {
  const zones = {
    'Run': {
      'beginner': { min: 0.65, max: 0.75, target: 0.70 },
      'intermediate': { min: 0.70, max: 0.80, target: 0.75 },
      'advanced': { min: 0.75, max: 0.85, target: 0.80 }
    },
    'Bike': {
      'beginner': { min: 0.60, max: 0.70, target: 0.65 },
      'intermediate': { min: 0.65, max: 0.75, target: 0.70 },
      'advanced': { min: 0.70, max: 0.80, target: 0.75 }
    },
    'Walk': {
      'beginner': { min: 0.50, max: 0.60, target: 0.55 },
      'intermediate': { min: 0.55, max: 0.65, target: 0.60 },
      'advanced': { min: 0.60, max: 0.70, target: 0.65 }
    }
  };

  const zone = zones[activityType][fitnessLevel];
  return {
    min: Math.round(maxHR * zone.min),
    max: Math.round(maxHR * zone.max),
    target: Math.round(maxHR * zone.target)
  };
}

/**
 * Generate realistic heart rate curve for an activity
 */
function generateHeartRateCurve(
  duration: number,
  targetHR: number,
  minHR: number,
  maxHR: number,
  activityType: 'Run' | 'Bike' | 'Walk'
): number[] {
  const points = Math.ceil(duration / 5); // HR point every 5 seconds
  const curve: number[] = [];

  // Activity-specific patterns
  const patterns = {
    'Run': {
      warmupDuration: 0.15, // 15% of activity for warmup
      peakDuration: 0.60,   // 60% at target intensity
      cooldownDuration: 0.25 // 25% for cooldown
    },
    'Bike': {
      warmupDuration: 0.20,
      peakDuration: 0.65,
      cooldownDuration: 0.15
    },
    'Walk': {
      warmupDuration: 0.10,
      peakDuration: 0.80,
      cooldownDuration: 0.10
    }
  };

  const pattern = patterns[activityType];
  const warmupPoints = Math.floor(points * pattern.warmupDuration);
  const peakPoints = Math.floor(points * pattern.peakDuration);
  const cooldownPoints = points - warmupPoints - peakPoints;

  // Warmup phase - gradual increase
  for (let i = 0; i < warmupPoints; i++) {
    const progress = i / warmupPoints;
    const hr = minHR + (targetHR - minHR) * Math.pow(progress, 0.7);
    curve.push(hr);
  }

  // Peak phase - around target with variations
  for (let i = 0; i < peakPoints; i++) {
    const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
    const hr = targetHR * (1 + variation);
    curve.push(Math.max(minHR, Math.min(maxHR, hr)));
  }

  // Cooldown phase - gradual decrease
  for (let i = 0; i < cooldownPoints; i++) {
    const progress = i / cooldownPoints;
    const hr = targetHR - (targetHR - minHR) * Math.pow(progress, 0.5);
    curve.push(hr);
  }

  return curve;
}

/**
 * Add realistic noise and variations to heart rate data
 */
function addHeartRateNoise(heartRates: number[], intensity: number = 0.05): number[] {
  return heartRates.map(hr => {
    // Add Gaussian noise
    const noise = (Math.random() + Math.random() + Math.random() + Math.random() - 2) * intensity * hr;
    return Math.round(Math.max(60, Math.min(220, hr + noise)));
  });
}

/**
 * Generate heart rate variability (HRV) data
 */
function generateHRV(heartRates: number[], fitnessLevel: 'beginner' | 'intermediate' | 'advanced'): number[] {
  // HRV baseline values (RMSSD in ms)
  const baselineHRV = {
    'beginner': 25,
    'intermediate': 35,
    'advanced': 45
  };

  const baseline = baselineHRV[fitnessLevel];

  return heartRates.map(hr => {
    // HRV decreases with higher heart rate
    const hrFactor = Math.max(0.3, 1 - (hr - 60) / 160);
    const variation = (Math.random() - 0.5) * 0.4; // ±20% variation
    return Math.round(baseline * hrFactor * (1 + variation));
  });
}

/**
 * Main function to generate realistic heart rate data
 */
export function generateHeartRateData(
  timestamps: Date[],
  options: HeartRateOptions
): HeartRateData[] {
  const {
    activityType,
    duration,
    userAge = 35,
    fitnessLevel = 'intermediate',
    averageHeartRate,
    includeHRV = true
  } = options;

  const maxHR = calculateMaxHeartRate(userAge);
  const zones = getTargetHeartRateZones(maxHR, activityType, fitnessLevel);
  
  const targetHR = averageHeartRate || zones.target;
  const minHR = Math.max(60, targetHR - 30);
  const maxHR_activity = Math.min(maxHR, targetHR + 20);

  // Generate base heart rate curve
  const heartRateCurve = generateHeartRateCurve(
    duration,
    targetHR,
    minHR,
    maxHR_activity,
    activityType
  );

  // Add realistic noise
  const noisyHeartRates = addHeartRateNoise(heartRateCurve);

  // Generate HRV if requested
  const hrvData = includeHRV ? generateHRV(noisyHeartRates, fitnessLevel) : undefined;

  // Map to timestamps (interpolate if needed)
  const result: HeartRateData[] = [];
  const hrInterval = duration / heartRateCurve.length;

  timestamps.forEach((timestamp, index) => {
    const timeOffset = index * (duration / timestamps.length);
    const hrIndex = Math.floor(timeOffset / hrInterval);
    const clampedIndex = Math.min(hrIndex, noisyHeartRates.length - 1);

    result.push({
      timestamp,
      heartRate: noisyHeartRates[clampedIndex],
      hrv: hrvData ? hrvData[clampedIndex] : undefined
    });
  });

  return result;
}

/**
 * Calculate heart rate statistics
 */
export function calculateHeartRateStats(
  heartRateData: HeartRateData[],
  maxHR: number
): HeartRateStats {
  const heartRates = heartRateData.map(d => d.heartRate);
  const hrvValues = heartRateData.map(d => d.hrv).filter(Boolean) as number[];

  const averageHR = Math.round(heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length);
  const maxHeartRate = Math.max(...heartRates);
  const minHeartRate = Math.min(...heartRates);
  const averageHRV = hrvValues.length > 0 
    ? Math.round(hrvValues.reduce((sum, hrv) => sum + hrv, 0) / hrvValues.length)
    : undefined;

  // Calculate time in zones
  const zones = {
    zone1: 0, zone2: 0, zone3: 0, zone4: 0, zone5: 0
  };

  heartRates.forEach(hr => {
    const percentage = hr / maxHR;
    if (percentage < 0.6) zones.zone1++;
    else if (percentage < 0.7) zones.zone2++;
    else if (percentage < 0.8) zones.zone3++;
    else if (percentage < 0.9) zones.zone4++;
    else zones.zone5++;
  });

  const total = heartRates.length;
  Object.keys(zones).forEach(zone => {
    zones[zone as keyof typeof zones] = Math.round((zones[zone as keyof typeof zones] / total) * 100);
  });

  return {
    averageHR,
    maxHR: maxHeartRate,
    minHR: minHeartRate,
    averageHRV,
    zones
  };
}
